import numpy as np
import pandas as pd
from sklearn.preprocessing import StandardScaler
from sklearn.ensemble import RandomForestRegressor
from sklearn.linear_model import LinearRegression
from sklearn.metrics import mean_absolute_error, mean_squared_error
from sklearn.model_selection import train_test_split
import joblib
import os
from data_handler import StockDataHandler

class StockPredictorSklearn:
    def __init__(self, sequence_length=30):
        self.sequence_length = sequence_length
        self.model = None
        self.scaler_X = StandardScaler()
        self.scaler_y = StandardScaler()
        self.feature_columns = None
        self.data_handler = StockDataHandler()
        self.model_type = "RandomForest"  # or "LinearRegression"
        
    def build_model(self, model_type="RandomForest"):
        """Build sklearn model"""
        self.model_type = model_type
        
        if model_type == "RandomForest":
            model = RandomForestRegressor(
                n_estimators=100,
                max_depth=10,
                random_state=42,
                n_jobs=-1
            )
        elif model_type == "LinearRegression":
            model = LinearRegression()
        else:
            raise ValueError("Model type must be 'RandomForest' or 'LinearRegression'")
        
        return model
    
    def create_sequences(self, data, target_col_idx=3):  # Close price is at index 3
        """Create sequences for time series prediction"""
        X, y = [], []
        for i in range(self.sequence_length, len(data)):
            # Flatten the sequence for sklearn models
            X.append(data[i-self.sequence_length:i].flatten())
            y.append(data[i, target_col_idx])
        return np.array(X), np.array(y)
    
    def prepare_training_data(self, ticker, period="2y"):
        """Prepare data for training"""
        # Download and prepare data
        raw_data = self.data_handler.download_stock_data(ticker, period)
        X_seq, y_seq, feature_columns = self.data_handler.prepare_data_for_ml(
            raw_data, sequence_length=self.sequence_length
        )
        
        self.feature_columns = feature_columns
        
        # Flatten sequences for sklearn
        X_flat = X_seq.reshape(X_seq.shape[0], -1)
        
        # Split data
        X_train, X_test, y_train, y_test = train_test_split(
            X_flat, y_seq, test_size=0.2, random_state=42, shuffle=False
        )
        
        # Scale data
        X_train_scaled = self.scaler_X.fit_transform(X_train)
        X_test_scaled = self.scaler_X.transform(X_test)
        
        y_train_scaled = self.scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
        y_test_scaled = self.scaler_y.transform(y_test.reshape(-1, 1)).flatten()
        
        return (X_train_scaled, y_train_scaled, X_test_scaled, y_test_scaled, 
                y_train, y_test, raw_data)
    
    def train(self, ticker, period="2y", model_type="RandomForest"):
        """Train the model"""
        print(f"Preparing data for {ticker}...")
        X_train, y_train, X_test, y_test, y_train_orig, y_test_orig, raw_data = \
            self.prepare_training_data(ticker, period)
        
        print(f"Building {model_type} model...")
        self.model = self.build_model(model_type)
        
        print(f"Training model...")
        self.model.fit(X_train, y_train)
        
        # Make predictions
        print("Evaluating model...")
        train_pred_scaled = self.model.predict(X_train)
        test_pred_scaled = self.model.predict(X_test)
        
        # Inverse transform predictions
        train_pred = self.scaler_y.inverse_transform(train_pred_scaled.reshape(-1, 1)).flatten()
        test_pred = self.scaler_y.inverse_transform(test_pred_scaled.reshape(-1, 1)).flatten()
        
        # Calculate metrics
        train_mae = mean_absolute_error(y_train_orig, train_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train_orig, train_pred))
        test_mae = mean_absolute_error(y_test_orig, test_pred)
        test_rmse = np.sqrt(mean_squared_error(y_test_orig, test_pred))
        
        # Calculate MAPE
        train_mape = np.mean(np.abs((y_train_orig - train_pred) / y_train_orig)) * 100
        test_mape = np.mean(np.abs((y_test_orig - test_pred) / y_test_orig)) * 100
        
        metrics = {
            'train_mae': train_mae,
            'train_rmse': train_rmse,
            'train_mape': train_mape,
            'test_mae': test_mae,
            'test_rmse': test_rmse,
            'test_mape': test_mape
        }
        
        # Feature importance for RandomForest
        if model_type == "RandomForest":
            feature_names = []
            for i in range(self.sequence_length):
                for col in self.feature_columns:
                    feature_names.append(f"{col}_t-{self.sequence_length-i}")
            
            feature_importance = dict(zip(feature_names, self.model.feature_importances_))
            metrics['feature_importance'] = feature_importance
        
        return None, metrics, raw_data, (y_test_orig, test_pred)
    
    def predict_future(self, ticker, days=5):
        """Predict future stock prices"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        # Get latest data
        raw_data = self.data_handler.download_stock_data(ticker, period="1y")
        
        # Prepare data with technical indicators
        df = self.data_handler.add_technical_indicators(raw_data)
        feature_columns = [
            'Open', 'High', 'Low', 'Close', 'Volume',
            'MA_5', 'MA_10', 'MA_20', 'RSI', 'MACD', 
            'MACD_signal', 'BB_upper', 'BB_lower', 'BB_middle',
            'Volume_MA', 'Price_Change', 'Price_Change_MA'
        ]
        
        df = df.dropna()
        features = df[feature_columns].values
        
        if len(features) < self.sequence_length:
            raise ValueError(f"Not enough data for prediction. Need at least {self.sequence_length} rows")
        
        predictions = []
        current_sequence = features[-self.sequence_length:].copy()
        
        for _ in range(days):
            # Flatten sequence for sklearn
            sequence_flat = current_sequence.flatten().reshape(1, -1)
            
            # Scale the data
            sequence_scaled = self.scaler_X.transform(sequence_flat)
            
            # Predict next value
            pred_scaled = self.model.predict(sequence_scaled)
            pred = self.scaler_y.inverse_transform(pred_scaled.reshape(-1, 1))[0, 0]
            predictions.append(pred)
            
            # Update sequence for next prediction
            # This is a simplified approach - we update only the close price
            new_row = current_sequence[-1].copy()
            new_row[3] = pred  # Update close price (index 3)
            
            # Shift sequence and add new row
            current_sequence = np.roll(current_sequence, -1, axis=0)
            current_sequence[-1] = new_row
        
        return predictions
    
    def save_model(self, filepath):
        """Save the trained model and scalers"""
        if self.model is None:
            raise ValueError("No model to save!")
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Save model
        joblib.dump(self.model, f"{filepath}_model.pkl")
        
        # Save scalers and metadata
        metadata = {
            'scaler_X': self.scaler_X,
            'scaler_y': self.scaler_y,
            'sequence_length': self.sequence_length,
            'feature_columns': self.feature_columns,
            'model_type': self.model_type
        }
        joblib.dump(metadata, f"{filepath}_metadata.pkl")
        
    def load_model(self, filepath):
        """Load a trained model and scalers"""
        # Load model
        self.model = joblib.load(f"{filepath}_model.pkl")
        
        # Load scalers and metadata
        metadata = joblib.load(f"{filepath}_metadata.pkl")
        self.scaler_X = metadata['scaler_X']
        self.scaler_y = metadata['scaler_y']
        self.sequence_length = metadata['sequence_length']
        self.feature_columns = metadata['feature_columns']
        self.model_type = metadata.get('model_type', 'RandomForest')
    
    def get_feature_importance(self):
        """Get feature importance for RandomForest model"""
        if self.model is None or self.model_type != "RandomForest":
            return None
        
        if hasattr(self.model, 'feature_importances_'):
            feature_names = []
            for i in range(self.sequence_length):
                for col in self.feature_columns:
                    feature_names.append(f"{col}_t-{self.sequence_length-i}")
            
            importance_dict = dict(zip(feature_names, self.model.feature_importances_))
            # Sort by importance
            sorted_importance = sorted(importance_dict.items(), key=lambda x: x[1], reverse=True)
            return sorted_importance[:20]  # Top 20 features
        
        return None
