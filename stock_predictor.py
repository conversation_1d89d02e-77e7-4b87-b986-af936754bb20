import numpy as np
import pandas as pd
from sklearn.preprocessing import MinMaxScaler
from sklearn.metrics import mean_absolute_error, mean_squared_error
import tensorflow as tf
from tensorflow.keras.models import Sequential
from tensorflow.keras.layers import LSTM, Dense, Dropout
from tensorflow.keras.optimizers import <PERSON>
from tensorflow.keras.callbacks import EarlyStopping, ReduceLROnPlateau
import joblib
import os
from data_handler import StockDataHandler

class StockPredictor:
    def __init__(self, sequence_length=60):
        self.sequence_length = sequence_length
        self.model = None
        self.scaler_X = MinMaxScaler()
        self.scaler_y = MinMaxScaler()
        self.feature_columns = None
        self.data_handler = StockDataHandler()
        
    def build_model(self, input_shape):
        """Build LSTM model"""
        model = Sequential([
            LSTM(50, return_sequences=True, input_shape=input_shape),
            Dropout(0.2),
            LSTM(50, return_sequences=True),
            Dropout(0.2),
            LSTM(50, return_sequences=False),
            Dropout(0.2),
            <PERSON><PERSON>(25),
            <PERSON><PERSON>(1)
        ])
        
        model.compile(
            optimizer=Adam(learning_rate=0.001),
            loss='mse',
            metrics=['mae']
        )
        
        return model
    
    def prepare_training_data(self, ticker, period="2y"):
        """Prepare data for training"""
        # Download and prepare data
        raw_data = self.data_handler.download_stock_data(ticker, period)
        X, y, feature_columns = self.data_handler.prepare_data_for_ml(
            raw_data, sequence_length=self.sequence_length
        )
        
        self.feature_columns = feature_columns
        
        # Split data
        split_index = int(len(X) * 0.8)
        X_train, X_test = X[:split_index], X[split_index:]
        y_train, y_test = y[:split_index], y[split_index:]
        
        # Scale data
        X_train_scaled = self.scaler_X.fit_transform(
            X_train.reshape(-1, X_train.shape[-1])
        ).reshape(X_train.shape)
        
        X_test_scaled = self.scaler_X.transform(
            X_test.reshape(-1, X_test.shape[-1])
        ).reshape(X_test.shape)
        
        y_train_scaled = self.scaler_y.fit_transform(y_train.reshape(-1, 1)).flatten()
        y_test_scaled = self.scaler_y.transform(y_test.reshape(-1, 1)).flatten()
        
        return (X_train_scaled, y_train_scaled, X_test_scaled, y_test_scaled, 
                y_train, y_test, raw_data)
    
    def train(self, ticker, period="2y", epochs=100, batch_size=32, validation_split=0.2):
        """Train the model"""
        print(f"Preparing data for {ticker}...")
        X_train, y_train, X_test, y_test, y_train_orig, y_test_orig, raw_data = \
            self.prepare_training_data(ticker, period)
        
        print(f"Building model...")
        self.model = self.build_model((X_train.shape[1], X_train.shape[2]))
        
        # Callbacks
        early_stopping = EarlyStopping(
            monitor='val_loss', patience=10, restore_best_weights=True
        )
        reduce_lr = ReduceLROnPlateau(
            monitor='val_loss', factor=0.2, patience=5, min_lr=0.0001
        )
        
        print(f"Training model...")
        history = self.model.fit(
            X_train, y_train,
            epochs=epochs,
            batch_size=batch_size,
            validation_split=validation_split,
            callbacks=[early_stopping, reduce_lr],
            verbose=1
        )
        
        # Evaluate model
        print("Evaluating model...")
        train_pred_scaled = self.model.predict(X_train)
        test_pred_scaled = self.model.predict(X_test)
        
        # Inverse transform predictions
        train_pred = self.scaler_y.inverse_transform(train_pred_scaled).flatten()
        test_pred = self.scaler_y.inverse_transform(test_pred_scaled).flatten()
        
        # Calculate metrics
        train_mae = mean_absolute_error(y_train_orig, train_pred)
        train_rmse = np.sqrt(mean_squared_error(y_train_orig, train_pred))
        test_mae = mean_absolute_error(y_test_orig, test_pred)
        test_rmse = np.sqrt(mean_squared_error(y_test_orig, test_pred))
        
        # Calculate MAPE
        train_mape = np.mean(np.abs((y_train_orig - train_pred) / y_train_orig)) * 100
        test_mape = np.mean(np.abs((y_test_orig - test_pred) / y_test_orig)) * 100
        
        metrics = {
            'train_mae': train_mae,
            'train_rmse': train_rmse,
            'train_mape': train_mape,
            'test_mae': test_mae,
            'test_rmse': test_rmse,
            'test_mape': test_mape
        }
        
        return history, metrics, raw_data, (y_test_orig, test_pred)
    
    def predict_future(self, ticker, days=5):
        """Predict future stock prices"""
        if self.model is None:
            raise ValueError("Model not trained yet!")
        
        # Get latest data
        raw_data = self.data_handler.download_stock_data(ticker, period="1y")
        latest_sequence = self.data_handler.get_latest_data_for_prediction(
            raw_data, self.sequence_length
        )
        
        # Scale the data
        latest_sequence_scaled = self.scaler_X.transform(
            latest_sequence.reshape(-1, latest_sequence.shape[-1])
        ).reshape(latest_sequence.shape)
        
        predictions = []
        current_sequence = latest_sequence_scaled.copy()
        
        for _ in range(days):
            # Predict next value
            pred_scaled = self.model.predict(current_sequence, verbose=0)
            pred = self.scaler_y.inverse_transform(pred_scaled)[0, 0]
            predictions.append(pred)
            
            # Update sequence for next prediction
            # This is a simplified approach - in practice, you'd want to update
            # all features, not just the close price
            new_row = current_sequence[0, -1, :].copy()
            new_row[3] = pred_scaled[0, 0]  # Update close price (index 3)
            
            current_sequence = np.roll(current_sequence, -1, axis=1)
            current_sequence[0, -1, :] = new_row
        
        return predictions
    
    def save_model(self, filepath):
        """Save the trained model and scalers"""
        if self.model is None:
            raise ValueError("No model to save!")
        
        # Create directory if it doesn't exist
        os.makedirs(os.path.dirname(filepath), exist_ok=True)
        
        # Save model
        self.model.save(f"{filepath}_model.h5")
        
        # Save scalers and metadata
        metadata = {
            'scaler_X': self.scaler_X,
            'scaler_y': self.scaler_y,
            'sequence_length': self.sequence_length,
            'feature_columns': self.feature_columns
        }
        joblib.dump(metadata, f"{filepath}_metadata.pkl")
        
    def load_model(self, filepath):
        """Load a trained model and scalers"""
        # Load model
        self.model = tf.keras.models.load_model(f"{filepath}_model.h5")
        
        # Load scalers and metadata
        metadata = joblib.load(f"{filepath}_metadata.pkl")
        self.scaler_X = metadata['scaler_X']
        self.scaler_y = metadata['scaler_y']
        self.sequence_length = metadata['sequence_length']
        self.feature_columns = metadata['feature_columns']
