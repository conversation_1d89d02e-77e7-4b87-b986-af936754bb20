# 🚀 Quick Start Guide - Stock Predictor

## Langkah Cepat untuk Memulai

### 1. Jalankan Aplikasi
```bash
# Klik dua kali file ini:
run_app.bat

# Atau jalankan manual:
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe main_sklearn.py
```

### 2. Prediksi Pertama Anda
1. **Masukkan ticker**: `AAPL` (atau `GOOGL`, `MSFT`, `TSLA`)
2. **Pilih period**: `1y` (1 tahun data)
3. **Klik "Load Data"** - tunggu beberapa detik
4. **Pilih model**: `RandomForest` (recommended)
5. **Klik "Train Model"** - tunggu 1-2 menit
6. **Klik "Predict Future"** - lihat prediksi 5 hari ke depan!

### 3. Fitur Lanjutan
- **Feature Importance**: Lihat faktor apa yang paling mempengaruhi harga
- **Save Model**: Simpan model untuk digunakan lagi
- **Load Model**: Muat model yang sudah disimpan

## 🎯 Tips untuk Hasil Terbaik

### Pi<PERSON>han Ticker Terbaik
- **Tech Stocks**: AAPL, GOOGL, MSFT, NVDA
- **Popular Stocks**: TSLA, AMZN, META, NFLX
- **Index ETFs**: SPY, QQQ, IWM

### Pengaturan Optimal
- **Period**: `2y` untuk akurasi terbaik
- **Model**: `RandomForest` untuk stabilitas
- **Sequence Length**: `30` (default sudah optimal)

### Interpretasi Hasil
- **MAPE < 5%**: Model sangat baik ✅
- **MAPE 5-10%**: Model baik 👍
- **MAPE > 10%**: Model perlu improvement ⚠️

## 🔧 Troubleshooting

### Error "No data found"
- Pastikan ticker benar (gunakan huruf kapital)
- Coba ticker yang lebih populer (AAPL, GOOGL)
- Periksa koneksi internet

### Training terlalu lama
- Kurangi period data (gunakan `1y` instead of `5y`)
- Kurangi sequence length (gunakan `20` instead of `30`)

### Prediksi tidak akurat
- Gunakan period data lebih panjang (`2y` atau `5y`)
- Coba model RandomForest
- Periksa MAPE - jika > 10%, model perlu improvement

## 📊 Contoh Sesi Lengkap

```
1. Load Data: AAPL, 2y ✅
   Result: 504 data points loaded

2. Train Model: RandomForest ✅
   Result: Test MAPE: 3.2% (Excellent!)

3. Predict Future: 5 days ✅
   Day 1: $185.50 📈 (****%)
   Day 2: $186.20 📈 (****%)
   Day 3: $185.80 📉 (****%)
   Day 4: $187.10 📈 (****%)
   Day 5: $186.90 📉 (****%)

4. Feature Importance ✅
   Top features: Close_t-1, MA_20_t-2, RSI_t-3
```

## ⚠️ Disclaimer Penting

**PERINGATAN**: Ini adalah sistem edukasi. Jangan gunakan prediksi ini sebagai satu-satunya dasar investasi. Selalu lakukan riset tambahan dan konsultasi dengan ahli keuangan.

## 🎬 Demo Mode

Untuk test cepat tanpa GUI:
```bash
C:\Users\<USER>\AppData\Local\Programs\Python\Python313\python.exe demo.py
```

## 📞 Bantuan

Jika mengalami masalah:
1. Baca file `README.md` untuk dokumentasi lengkap
2. Jalankan `test_predictor.py` untuk validasi sistem
3. Pastikan semua dependencies terinstall dengan benar

---

**Happy Predicting! 📈🚀**
