import tkinter as tk
from tkinter import ttk, messagebox, filedialog
import matplotlib.pyplot as plt
from matplotlib.backends.backend_tkagg import FigureCanvasTkAgg
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import threading
from stock_predictor_sklearn import StockPredictorSklearn
from data_handler import StockDataHandler
import os

class StockPredictorGUI:
    def __init__(self, root):
        self.root = root
        self.root.title("Stock Price Predictor - ML System (Sklearn Version)")
        self.root.geometry("1200x800")
        
        # Initialize components
        self.predictor = StockPredictorSklearn()
        self.data_handler = StockDataHandler()
        self.current_data = None
        
        self.setup_gui()
        
    def setup_gui(self):
        # Create main frame
        main_frame = ttk.Frame(self.root, padding="10")
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure grid weights
        self.root.columnconfigure(0, weight=1)
        self.root.rowconfigure(0, weight=1)
        main_frame.columnconfigure(1, weight=1)
        main_frame.rowconfigure(2, weight=1)
        
        # Left panel for controls
        control_frame = ttk.LabelFrame(main_frame, text="Controls", padding="10")
        control_frame.grid(row=0, column=0, rowspan=3, sticky=(tk.W, tk.E, tk.N, tk.S), padx=(0, 10))
        
        # Stock ticker input
        ttk.Label(control_frame, text="Stock Ticker:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.ticker_var = tk.StringVar(value="AAPL")
        ticker_entry = ttk.Entry(control_frame, textvariable=self.ticker_var, width=15)
        ticker_entry.grid(row=0, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Period selection
        ttk.Label(control_frame, text="Data Period:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.period_var = tk.StringVar(value="2y")
        period_combo = ttk.Combobox(control_frame, textvariable=self.period_var, 
                                   values=["1y", "2y", "5y", "10y"], width=12)
        period_combo.grid(row=1, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Model type selection
        ttk.Label(control_frame, text="Model Type:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.model_type_var = tk.StringVar(value="RandomForest")
        model_combo = ttk.Combobox(control_frame, textvariable=self.model_type_var, 
                                  values=["RandomForest", "LinearRegression"], width=12)
        model_combo.grid(row=2, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Sequence length
        ttk.Label(control_frame, text="Sequence Length:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.sequence_var = tk.StringVar(value="30")
        sequence_entry = ttk.Entry(control_frame, textvariable=self.sequence_var, width=15)
        sequence_entry.grid(row=3, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Prediction days
        ttk.Label(control_frame, text="Predict Days:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.predict_days_var = tk.StringVar(value="5")
        predict_entry = ttk.Entry(control_frame, textvariable=self.predict_days_var, width=15)
        predict_entry.grid(row=4, column=1, sticky=(tk.W, tk.E), pady=2)
        
        # Buttons
        ttk.Button(control_frame, text="Load Data", 
                  command=self.load_data).grid(row=5, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        ttk.Button(control_frame, text="Train Model", 
                  command=self.train_model).grid(row=6, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Button(control_frame, text="Predict Future", 
                  command=self.predict_future).grid(row=7, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Button(control_frame, text="Feature Importance", 
                  command=self.show_feature_importance).grid(row=8, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        # Model management
        ttk.Separator(control_frame, orient='horizontal').grid(row=9, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=10)
        
        ttk.Button(control_frame, text="Save Model", 
                  command=self.save_model).grid(row=10, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        ttk.Button(control_frame, text="Load Model", 
                  command=self.load_model).grid(row=11, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        # Progress bar
        self.progress_var = tk.DoubleVar()
        self.progress_bar = ttk.Progressbar(control_frame, variable=self.progress_var, 
                                          mode='indeterminate')
        self.progress_bar.grid(row=12, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=5)
        
        # Status label
        self.status_var = tk.StringVar(value="Ready")
        status_label = ttk.Label(control_frame, textvariable=self.status_var, foreground="blue")
        status_label.grid(row=13, column=0, columnspan=2, sticky=(tk.W, tk.E), pady=2)
        
        # Right panel for results
        results_frame = ttk.LabelFrame(main_frame, text="Results", padding="10")
        results_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_frame.columnconfigure(0, weight=1)
        results_frame.rowconfigure(0, weight=1)
        
        # Results text
        self.results_text = tk.Text(results_frame, height=8, wrap=tk.WORD)
        results_scrollbar = ttk.Scrollbar(results_frame, orient="vertical", command=self.results_text.yview)
        self.results_text.configure(yscrollcommand=results_scrollbar.set)
        
        self.results_text.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        results_scrollbar.grid(row=0, column=1, sticky=(tk.N, tk.S))
        
        # Chart frame
        chart_frame = ttk.LabelFrame(main_frame, text="Chart", padding="10")
        chart_frame.grid(row=1, column=1, rowspan=2, sticky=(tk.W, tk.E, tk.N, tk.S), pady=(10, 0))
        chart_frame.columnconfigure(0, weight=1)
        chart_frame.rowconfigure(0, weight=1)
        
        # Create matplotlib figure
        self.fig, self.ax = plt.subplots(figsize=(8, 6))
        self.canvas = FigureCanvasTkAgg(self.fig, chart_frame)
        self.canvas.get_tk_widget().grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # Configure column weights
        control_frame.columnconfigure(1, weight=1)
        
    def update_status(self, message):
        self.status_var.set(message)
        self.root.update_idletasks()
        
    def update_results(self, text):
        self.results_text.insert(tk.END, text + "\n")
        self.results_text.see(tk.END)
        self.root.update_idletasks()
        
    def clear_results(self):
        self.results_text.delete(1.0, tk.END)
        
    def load_data(self):
        """Load stock data"""
        def load_data_thread():
            try:
                self.progress_bar.start()
                self.update_status("Loading data...")
                
                ticker = self.ticker_var.get().upper()
                period = self.period_var.get()
                
                if not ticker:
                    messagebox.showerror("Error", "Please enter a stock ticker")
                    return
                
                # Load data
                self.current_data = self.data_handler.download_stock_data(ticker, period)
                
                # Update results
                self.clear_results()
                self.update_results(f"Data loaded for {ticker}")
                self.update_results(f"Period: {period}")
                self.update_results(f"Data points: {len(self.current_data)}")
                self.update_results(f"Date range: {self.current_data.index[0].date()} to {self.current_data.index[-1].date()}")
                
                # Plot data
                self.plot_stock_data()
                
                self.update_status("Data loaded successfully")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to load data: {str(e)}")
                self.update_status("Error loading data")
            finally:
                self.progress_bar.stop()
        
        threading.Thread(target=load_data_thread, daemon=True).start()
        
    def plot_stock_data(self):
        """Plot stock data"""
        if self.current_data is None:
            return
            
        self.ax.clear()
        self.ax.plot(self.current_data.index, self.current_data['Close'], label='Close Price', linewidth=1)
        self.ax.set_title(f'{self.ticker_var.get().upper()} Stock Price')
        self.ax.set_xlabel('Date')
        self.ax.set_ylabel('Price ($)')
        self.ax.legend()
        self.ax.grid(True, alpha=0.3)
        
        # Format x-axis
        self.fig.autofmt_xdate()
        self.canvas.draw()
        
    def train_model(self):
        """Train the ML model"""
        def train_thread():
            try:
                self.progress_bar.start()
                self.update_status("Training model...")
                
                ticker = self.ticker_var.get().upper()
                period = self.period_var.get()
                model_type = self.model_type_var.get()
                sequence_length = int(self.sequence_var.get())
                
                if not ticker:
                    messagebox.showerror("Error", "Please enter a stock ticker")
                    return
                
                # Update predictor sequence length
                self.predictor.sequence_length = sequence_length
                
                self.clear_results()
                self.update_results("Starting model training...")
                self.update_results(f"Ticker: {ticker}")
                self.update_results(f"Period: {period}")
                self.update_results(f"Model Type: {model_type}")
                self.update_results(f"Sequence Length: {sequence_length}")
                self.update_results("-" * 40)
                
                # Train model
                history, metrics, data, test_results = self.predictor.train(
                    ticker, period, model_type
                )
                
                self.current_data = data
                
                # Display results
                self.update_results("Training completed!")
                self.update_results(f"Train MAE: {metrics['train_mae']:.2f}")
                self.update_results(f"Train RMSE: {metrics['train_rmse']:.2f}")
                self.update_results(f"Train MAPE: {metrics['train_mape']:.2f}%")
                self.update_results(f"Test MAE: {metrics['test_mae']:.2f}")
                self.update_results(f"Test RMSE: {metrics['test_rmse']:.2f}")
                self.update_results(f"Test MAPE: {metrics['test_mape']:.2f}%")
                
                # Plot results
                self.plot_training_results(test_results)
                
                self.update_status("Model trained successfully")
                
            except Exception as e:
                messagebox.showerror("Error", f"Failed to train model: {str(e)}")
                self.update_status("Error training model")
            finally:
                self.progress_bar.stop()
        
        threading.Thread(target=train_thread, daemon=True).start()

    def plot_training_results(self, test_results):
        """Plot training results with predictions"""
        y_test, test_pred = test_results

        self.ax.clear()

        # Plot actual vs predicted
        dates = self.current_data.index[-len(y_test):]
        self.ax.plot(dates, y_test, label='Actual', linewidth=2, alpha=0.7)
        self.ax.plot(dates, test_pred, label='Predicted', linewidth=2, alpha=0.7)

        self.ax.set_title(f'{self.ticker_var.get().upper()} - Actual vs Predicted')
        self.ax.set_xlabel('Date')
        self.ax.set_ylabel('Price ($)')
        self.ax.legend()
        self.ax.grid(True, alpha=0.3)

        self.fig.autofmt_xdate()
        self.canvas.draw()

    def predict_future(self):
        """Predict future stock prices"""
        def predict_thread():
            try:
                if self.predictor.model is None:
                    messagebox.showerror("Error", "Please train a model first")
                    return

                self.progress_bar.start()
                self.update_status("Predicting future prices...")

                ticker = self.ticker_var.get().upper()
                days = int(self.predict_days_var.get())

                # Make predictions
                predictions = self.predictor.predict_future(ticker, days)

                # Display results
                self.update_results("-" * 40)
                self.update_results(f"Future predictions for {ticker}:")

                current_date = datetime.now().date()
                for i, pred in enumerate(predictions):
                    future_date = current_date + timedelta(days=i+1)
                    self.update_results(f"Day {i+1} ({future_date}): ${pred:.2f}")

                # Plot predictions
                self.plot_future_predictions(predictions)

                self.update_status("Predictions completed")

            except Exception as e:
                messagebox.showerror("Error", f"Failed to predict: {str(e)}")
                self.update_status("Error making predictions")
            finally:
                self.progress_bar.stop()

        threading.Thread(target=predict_thread, daemon=True).start()

    def plot_future_predictions(self, predictions):
        """Plot future predictions"""
        if self.current_data is None:
            return

        self.ax.clear()

        # Plot historical data (last 60 days)
        recent_data = self.current_data.tail(60)
        self.ax.plot(recent_data.index, recent_data['Close'],
                    label='Historical', linewidth=2, color='blue')

        # Plot predictions
        last_date = self.current_data.index[-1]
        future_dates = [last_date + timedelta(days=i+1) for i in range(len(predictions))]

        # Connect last historical point with first prediction
        connect_dates = [last_date] + future_dates
        connect_prices = [self.current_data['Close'].iloc[-1]] + predictions

        self.ax.plot(connect_dates, connect_prices,
                    label='Predicted', linewidth=2, color='red', linestyle='--')

        self.ax.set_title(f'{self.ticker_var.get().upper()} - Historical & Future Predictions')
        self.ax.set_xlabel('Date')
        self.ax.set_ylabel('Price ($)')
        self.ax.legend()
        self.ax.grid(True, alpha=0.3)

        self.fig.autofmt_xdate()
        self.canvas.draw()

    def show_feature_importance(self):
        """Show feature importance for RandomForest model"""
        if self.predictor.model is None:
            messagebox.showerror("Error", "Please train a model first")
            return

        if self.predictor.model_type != "RandomForest":
            messagebox.showinfo("Info", "Feature importance is only available for RandomForest model")
            return

        importance = self.predictor.get_feature_importance()
        if importance is None:
            messagebox.showerror("Error", "Could not get feature importance")
            return

        # Display top features
        self.update_results("-" * 40)
        self.update_results("Top 10 Most Important Features:")
        for i, (feature, score) in enumerate(importance[:10]):
            self.update_results(f"{i+1}. {feature}: {score:.4f}")

    def save_model(self):
        """Save the trained model"""
        try:
            if self.predictor.model is None:
                messagebox.showerror("Error", "No trained model to save")
                return

            filename = filedialog.asksaveasfilename(
                defaultextension=".model",
                filetypes=[("Model files", "*.model"), ("All files", "*.*")],
                title="Save Model"
            )

            if filename:
                # Remove extension if provided
                if filename.endswith('.model'):
                    filename = filename[:-6]

                self.predictor.save_model(filename)
                messagebox.showinfo("Success", "Model saved successfully!")
                self.update_results(f"Model saved to: {filename}")

        except Exception as e:
            messagebox.showerror("Error", f"Failed to save model: {str(e)}")

    def load_model(self):
        """Load a trained model"""
        try:
            filename = filedialog.askopenfilename(
                filetypes=[("Model files", "*.model"), ("All files", "*.*")],
                title="Load Model"
            )

            if filename:
                # Remove extension if provided
                if filename.endswith('.model'):
                    filename = filename[:-6]

                self.predictor.load_model(filename)
                messagebox.showinfo("Success", "Model loaded successfully!")
                self.update_results(f"Model loaded from: {filename}")
                self.update_status("Model loaded")

                # Update GUI with loaded model info
                self.model_type_var.set(self.predictor.model_type)
                self.sequence_var.set(str(self.predictor.sequence_length))

        except Exception as e:
            messagebox.showerror("Error", f"Failed to load model: {str(e)}")

def main():
    root = tk.Tk()
    app = StockPredictorGUI(root)
    root.mainloop()

if __name__ == "__main__":
    main()
