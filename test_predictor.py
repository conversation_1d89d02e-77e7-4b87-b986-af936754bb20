#!/usr/bin/env python3
"""
Test script untuk Stock Predictor
Menguji fungsi-fungsi utama tanpa GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_predictor_sklearn import StockPredictorSklearn
from data_handler import StockDataHandler
import matplotlib.pyplot as plt
import pandas as pd

def test_data_handler():
    """Test data handler functionality"""
    print("Testing Data Handler...")
    
    handler = StockDataHandler()
    
    try:
        # Test download data
        data = handler.download_stock_data("AAPL", "1y")
        print(f"✓ Downloaded AAPL data: {len(data)} rows")
        print(f"  Date range: {data.index[0].date()} to {data.index[-1].date()}")
        
        # Test technical indicators
        data_with_indicators = handler.add_technical_indicators(data)
        print(f"✓ Added technical indicators: {len(data_with_indicators.columns)} columns")
        
        # Test ML data preparation
        X, y, features = handler.prepare_data_for_ml(data, sequence_length=30)
        print(f"✓ Prepared ML data: X shape {X.shape}, y shape {y.shape}")
        print(f"  Features: {len(features)}")
        
        return True
        
    except Exception as e:
        print(f"✗ Data handler test failed: {e}")
        return False

def test_predictor():
    """Test predictor functionality"""
    print("\nTesting Stock Predictor...")
    
    predictor = StockPredictorSklearn(sequence_length=30)
    
    try:
        # Test training
        print("Training RandomForest model...")
        history, metrics, data, test_results = predictor.train("AAPL", "1y", "RandomForest")
        
        print("✓ Model training completed")
        print(f"  Test MAE: {metrics['test_mae']:.2f}")
        print(f"  Test RMSE: {metrics['test_rmse']:.2f}")
        print(f"  Test MAPE: {metrics['test_mape']:.2f}%")
        
        # Test prediction
        predictions = predictor.predict_future("AAPL", 5)
        print(f"✓ Future predictions: {predictions}")
        
        # Test feature importance
        importance = predictor.get_feature_importance()
        if importance:
            print("✓ Top 5 important features:")
            for i, (feature, score) in enumerate(importance[:5]):
                print(f"  {i+1}. {feature}: {score:.4f}")
        
        # Test save/load
        predictor.save_model("test_model")
        print("✓ Model saved")
        
        new_predictor = StockPredictorSklearn()
        new_predictor.load_model("test_model")
        print("✓ Model loaded")
        
        # Test prediction with loaded model
        new_predictions = new_predictor.predict_future("AAPL", 3)
        print(f"✓ Predictions with loaded model: {new_predictions}")
        
        return True
        
    except Exception as e:
        print(f"✗ Predictor test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_visualization():
    """Test visualization"""
    print("\nTesting Visualization...")
    
    try:
        handler = StockDataHandler()
        data = handler.download_stock_data("AAPL", "6m")
        
        # Create a simple plot
        plt.figure(figsize=(10, 6))
        plt.plot(data.index, data['Close'], label='AAPL Close Price')
        plt.title('AAPL Stock Price - Test Plot')
        plt.xlabel('Date')
        plt.ylabel('Price ($)')
        plt.legend()
        plt.grid(True, alpha=0.3)
        plt.tight_layout()
        
        # Save plot
        plt.savefig('test_plot.png', dpi=150, bbox_inches='tight')
        plt.close()
        
        print("✓ Visualization test completed - saved test_plot.png")
        return True
        
    except Exception as e:
        print(f"✗ Visualization test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("=" * 50)
    print("STOCK PREDICTOR TEST SUITE")
    print("=" * 50)
    
    tests = [
        ("Data Handler", test_data_handler),
        ("Stock Predictor", test_predictor),
        ("Visualization", test_visualization)
    ]
    
    results = []
    for test_name, test_func in tests:
        print(f"\n{test_name} Test:")
        print("-" * 30)
        result = test_func()
        results.append((test_name, result))
    
    # Summary
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    
    passed = 0
    for test_name, result in results:
        status = "PASSED" if result else "FAILED"
        print(f"{test_name}: {status}")
        if result:
            passed += 1
    
    print(f"\nTotal: {passed}/{len(tests)} tests passed")
    
    if passed == len(tests):
        print("\n🎉 All tests passed! The system is ready to use.")
    else:
        print(f"\n⚠️  {len(tests) - passed} test(s) failed. Please check the errors above.")

if __name__ == "__main__":
    main()
