# Stock Price Predictor - ML System

Sistem Machine Learning untuk memprediksi harga saham dengan antarmuka GUI berbasis Tkinter. Sistem ini menyediakan dua versi:

1. **Versi TensorFlow/LSTM** (`main.py`) - Menggunakan deep learning LSTM
2. **Versi Sklearn** (`main_sklearn.py`) - Menggunakan RandomForest dan Linear Regression (Kompatibel dengan Python 3.13)

## Fitur Utama

- **GUI yang User-Friendly**: Antarmuka grafis yang mudah digunakan dengan Tkinter
- **Multiple ML Models**:
  - LSTM Neural Network (TensorFlow)
  - Random Forest Regressor (Sklearn)
  - Linear Regression (Sklearn)
- **Technical Indicators**: Mengintegrasikan indikator teknis seperti RSI, MACD, Bollinger Bands
- **Visualisasi Real-time**: Grafik interaktif untuk data historis dan prediksi
- **Model Management**: Simpan dan muat model yang sudah ditraining
- **Multi-timeframe**: Support berbagai periode data (1y, 2y, 5y, 10y)
- **Feature Importance**: Analisis fitur penting untuk model RandomForest

## Struktur Proyek

```
ML Data/
├── main.py                    # GUI dengan TensorFlow/LSTM
├── main_sklearn.py           # GUI dengan Sklearn models (RECOMMENDED)
├── stock_predictor.py        # TensorFlow LSTM predictor
├── stock_predictor_sklearn.py # Sklearn predictor (RandomForest/LinearRegression)
├── data_handler.py           # Data processing dan technical indicators
├── test_predictor.py         # Test script untuk validasi sistem
├── run_app.bat              # Batch file untuk menjalankan aplikasi
├── requirements.txt         # Dependencies
├── README.md               # Dokumentasi
├── cache/                  # Cache data saham (dibuat otomatis)
└── models/                 # Saved models (dibuat otomatis)
```

## Instalasi

1. **Clone atau download proyek ini**

2. **Install dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

3. **Jalankan aplikasi:**

   **Opsi 1: Menggunakan Sklearn Version (RECOMMENDED untuk Python 3.13)**
   ```bash
   python main_sklearn.py
   ```

   **Opsi 2: Menggunakan batch file**
   ```bash
   run_app.bat
   ```

   **Opsi 3: TensorFlow Version (memerlukan TensorFlow)**
   ```bash
   python main.py
   ```

4. **Test sistem (opsional):**
   ```bash
   python test_predictor.py
   ```

## Cara Penggunaan

### 1. Load Data
- Masukkan ticker saham (contoh: AAPL, GOOGL, MSFT, TSLA)
- Pilih periode data (1y, 2y, 5y, 10y)
- Klik "Load Data" untuk mengunduh data historis

### 2. Train Model
- **Sklearn Version**:
  - Pilih model type: RandomForest atau LinearRegression
  - Atur sequence length (default: 30)
- **TensorFlow Version**:
  - Atur epochs (default: 50)
  - Atur batch size (default: 32)
- Klik "Train Model" untuk memulai training
- Tunggu hingga training selesai dan lihat metrik evaluasi

### 3. Prediksi Future
- Atur jumlah hari yang ingin diprediksi (default: 5)
- Klik "Predict Future" untuk membuat prediksi
- Lihat hasil prediksi di panel Results dan grafik

### 4. Feature Importance (Sklearn Version)
- Klik "Feature Importance" untuk melihat fitur paling penting
- Hanya tersedia untuk model RandomForest

### 5. Model Management
- **Save Model**: Simpan model yang sudah ditraining
- **Load Model**: Muat model yang sudah disimpan sebelumnya

## Technical Details

### Model Comparison

| Feature | TensorFlow/LSTM | Sklearn Version |
|---------|----------------|-----------------|
| **Compatibility** | Python < 3.13 | Python 3.13+ ✅ |
| **Training Speed** | Slower | Faster ✅ |
| **Memory Usage** | Higher | Lower ✅ |
| **Accuracy** | Higher (potentially) | Good |
| **Model Types** | LSTM only | RandomForest, LinearRegression |
| **Dependencies** | TensorFlow (large) | Sklearn (lightweight) ✅ |

### Model Architecture

**TensorFlow/LSTM Version:**
- **Input Layer**: Sequence length 60 hari dengan multiple features
- **LSTM Layers**: 3 layer LSTM dengan 50 units masing-masing
- **Dropout**: 0.2 untuk mencegah overfitting
- **Dense Layers**: 2 layer dense untuk output
- **Optimizer**: Adam dengan learning rate 0.001

**Sklearn Version:**
- **RandomForest**: 100 estimators, max_depth=10
- **LinearRegression**: Standard linear regression
- **Input**: Flattened sequences (sequence_length × features)
- **Preprocessing**: StandardScaler untuk normalisasi

### Features yang Digunakan
- **Price Data**: Open, High, Low, Close, Volume
- **Moving Averages**: MA 5, 10, 20 hari
- **Technical Indicators**:
  - RSI (Relative Strength Index)
  - MACD (Moving Average Convergence Divergence)
  - Bollinger Bands
  - Volume Moving Average
  - Price Change indicators

### Evaluasi Model
- **MAE** (Mean Absolute Error)
- **RMSE** (Root Mean Square Error)
- **MAPE** (Mean Absolute Percentage Error)

## Contoh Penggunaan

1. **Prediksi Saham Apple (AAPL)**:
   - Ticker: AAPL
   - Period: 2y
   - Epochs: 50
   - Predict Days: 5

2. **Prediksi Saham Tesla (TSLA)**:
   - Ticker: TSLA
   - Period: 1y
   - Epochs: 100
   - Predict Days: 10

## Tips Penggunaan

1. **Data Quality**: Gunakan periode data yang cukup panjang (minimal 1 tahun)
2. **Training Time**: Model dengan epochs lebih tinggi akan lebih akurat tapi butuh waktu lebih lama
3. **Validation**: Selalu periksa metrik evaluasi sebelum menggunakan prediksi
4. **Market Conditions**: Ingat bahwa prediksi ML tidak 100% akurat, terutama saat kondisi market volatil

## Troubleshooting

### Error "No data found for ticker"
- Pastikan ticker symbol benar (contoh: AAPL, bukan Apple)
- Periksa koneksi internet
- Coba ticker yang lebih populer

### Error "Not enough data"
- Gunakan periode data yang lebih panjang
- Pilih saham yang lebih aktif diperdagangkan

### Training terlalu lama
- Kurangi jumlah epochs
- Gunakan batch size yang lebih besar
- Gunakan periode data yang lebih pendek

## Disclaimer

⚠️ **PERINGATAN**: Sistem ini dibuat untuk tujuan edukasi dan penelitian. Prediksi harga saham tidak dapat dijamin akurat 100%. Jangan gunakan hasil prediksi ini sebagai satu-satunya dasar untuk keputusan investasi. Selalu lakukan riset tambahan dan konsultasi dengan ahli keuangan sebelum berinvestasi.

## Requirements

- Python 3.7+
- TensorFlow 2.x
- Tkinter (biasanya sudah terinstall dengan Python)
- Internet connection (untuk download data saham)

## Lisensi

Proyek ini dibuat untuk tujuan edukasi. Silakan gunakan dan modifikasi sesuai kebutuhan.
