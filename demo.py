#!/usr/bin/env python3
"""
Demo sederhana untuk Stock Predictor
Menunjukkan cara menggunakan sistem tanpa GUI
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from stock_predictor_sklearn import StockPredictorSklearn
from data_handler import StockDataHandler
import matplotlib.pyplot as plt
import pandas as pd
from datetime import datetime, timedelta

def demo_basic_prediction():
    """Demo prediksi dasar"""
    print("🚀 DEMO: Stock Price Prediction")
    print("=" * 50)
    
    # Initialize components
    predictor = StockPredictorSklearn(sequence_length=20)  # Shorter sequence for faster demo
    
    # Demo parameters
    ticker = "AAPL"
    period = "1y"
    model_type = "RandomForest"
    predict_days = 5
    
    print(f"📊 Ticker: {ticker}")
    print(f"📅 Period: {period}")
    print(f"🤖 Model: {model_type}")
    print(f"🔮 Predict: {predict_days} days")
    print("-" * 50)
    
    try:
        # Step 1: Train model
        print("1️⃣ Training model...")
        history, metrics, data, test_results = predictor.train(ticker, period, model_type)
        
        print("✅ Training completed!")
        print(f"   📈 Test MAE: ${metrics['test_mae']:.2f}")
        print(f"   📊 Test MAPE: {metrics['test_mape']:.1f}%")
        
        # Step 2: Make predictions
        print("\n2️⃣ Making predictions...")
        predictions = predictor.predict_future(ticker, predict_days)
        
        print("✅ Predictions completed!")
        current_price = data['Close'].iloc[-1]
        print(f"   💰 Current price: ${current_price:.2f}")
        
        print(f"\n🔮 Future predictions:")
        for i, pred in enumerate(predictions):
            future_date = datetime.now().date() + timedelta(days=i+1)
            change = pred - current_price
            change_pct = (change / current_price) * 100
            direction = "📈" if change > 0 else "📉"
            print(f"   Day {i+1} ({future_date}): ${pred:.2f} {direction} ({change_pct:+.1f}%)")
        
        # Step 3: Feature importance
        print("\n3️⃣ Feature importance (Top 5):")
        importance = predictor.get_feature_importance()
        if importance:
            for i, (feature, score) in enumerate(importance[:5]):
                print(f"   {i+1}. {feature}: {score:.3f}")
        
        # Step 4: Create visualization
        print("\n4️⃣ Creating visualization...")
        create_demo_plot(data, predictions, ticker)
        print("✅ Plot saved as 'demo_prediction.png'")
        
        return True
        
    except Exception as e:
        print(f"❌ Demo failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def create_demo_plot(data, predictions, ticker):
    """Create demo visualization"""
    plt.figure(figsize=(12, 8))
    
    # Plot historical data (last 60 days)
    recent_data = data.tail(60)
    plt.subplot(2, 1, 1)
    plt.plot(recent_data.index, recent_data['Close'], 'b-', linewidth=2, label='Historical Price')
    plt.title(f'{ticker} Stock Price - Last 60 Days')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    # Plot predictions
    plt.subplot(2, 1, 2)
    
    # Last 30 days + predictions
    last_30_days = data.tail(30)
    plt.plot(last_30_days.index, last_30_days['Close'], 'b-', linewidth=2, label='Historical')
    
    # Future predictions
    last_date = data.index[-1]
    future_dates = [last_date + timedelta(days=i+1) for i in range(len(predictions))]
    
    # Connect line
    connect_dates = [last_date] + future_dates
    connect_prices = [data['Close'].iloc[-1]] + predictions
    plt.plot(connect_dates, connect_prices, 'r--', linewidth=2, label='Predicted', marker='o')
    
    plt.title(f'{ticker} Price Prediction - Next {len(predictions)} Days')
    plt.xlabel('Date')
    plt.ylabel('Price ($)')
    plt.legend()
    plt.grid(True, alpha=0.3)
    
    plt.tight_layout()
    plt.savefig('demo_prediction.png', dpi=150, bbox_inches='tight')
    plt.close()

def demo_multiple_stocks():
    """Demo prediksi untuk beberapa saham"""
    print("\n🎯 DEMO: Multiple Stocks Comparison")
    print("=" * 50)
    
    stocks = ["AAPL", "GOOGL", "MSFT"]
    predictor = StockPredictorSklearn(sequence_length=15)  # Even shorter for speed
    
    results = {}
    
    for ticker in stocks:
        print(f"\n📊 Processing {ticker}...")
        try:
            # Quick training
            history, metrics, data, test_results = predictor.train(ticker, "6m", "RandomForest")
            
            # Quick prediction
            predictions = predictor.predict_future(ticker, 3)
            
            current_price = data['Close'].iloc[-1]
            avg_prediction = sum(predictions) / len(predictions)
            expected_return = ((avg_prediction - current_price) / current_price) * 100
            
            results[ticker] = {
                'current_price': current_price,
                'avg_prediction': avg_prediction,
                'expected_return': expected_return,
                'mape': metrics['test_mape']
            }
            
            print(f"   ✅ Current: ${current_price:.2f}")
            print(f"   🔮 Avg Prediction: ${avg_prediction:.2f}")
            print(f"   📈 Expected Return: {expected_return:+.1f}%")
            print(f"   📊 Model MAPE: {metrics['test_mape']:.1f}%")
            
        except Exception as e:
            print(f"   ❌ Failed: {e}")
    
    # Summary
    print(f"\n📋 SUMMARY:")
    print("-" * 30)
    for ticker, data in results.items():
        direction = "📈" if data['expected_return'] > 0 else "📉"
        print(f"{ticker}: {direction} {data['expected_return']:+.1f}% (MAPE: {data['mape']:.1f}%)")

def main():
    """Run demo"""
    print("🎬 Stock Predictor Demo")
    print("=" * 50)
    print("This demo will:")
    print("1. Train a RandomForest model on AAPL stock")
    print("2. Make 5-day price predictions")
    print("3. Show feature importance")
    print("4. Create visualization")
    print("5. Compare multiple stocks")
    print()
    
    input("Press Enter to start demo...")
    
    # Run basic demo
    success = demo_basic_prediction()
    
    if success:
        print("\n" + "=" * 50)
        choice = input("Run multiple stocks demo? (y/n): ").lower()
        if choice == 'y':
            demo_multiple_stocks()
    
    print("\n🎉 Demo completed!")
    print("📁 Check 'demo_prediction.png' for visualization")
    print("🚀 Run 'python main_sklearn.py' to use the GUI version")

if __name__ == "__main__":
    main()
