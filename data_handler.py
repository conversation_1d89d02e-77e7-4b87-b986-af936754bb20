import yfinance as yf
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
import ta
import os
import pickle

class StockDataHandler:
    def __init__(self, cache_dir="cache"):
        self.cache_dir = cache_dir
        if not os.path.exists(cache_dir):
            os.makedirs(cache_dir)
    
    def download_stock_data(self, ticker, period="2y", force_refresh=False):
        """Download stock data with caching"""
        cache_file = os.path.join(self.cache_dir, f"{ticker}_{period}.pkl")
        
        # Check if cached data exists and is recent
        if os.path.exists(cache_file) and not force_refresh:
            try:
                with open(cache_file, 'rb') as f:
                    cached_data = pickle.load(f)
                    # Check if data is from today
                    if cached_data['date'] == datetime.now().date():
                        return cached_data['data']
            except:
                pass
        
        try:
            # Download fresh data
            stock = yf.Ticker(ticker)
            data = stock.history(period=period)
            
            if data.empty:
                raise ValueError(f"No data found for ticker {ticker}")
            
            # Cache the data
            cache_data = {
                'date': datetime.now().date(),
                'data': data
            }
            with open(cache_file, 'wb') as f:
                pickle.dump(cache_data, f)
            
            return data
        
        except Exception as e:
            raise Exception(f"Error downloading data for {ticker}: {str(e)}")
    
    def add_technical_indicators(self, data):
        """Add technical indicators to the data"""
        df = data.copy()
        
        # Moving averages
        df['MA_5'] = ta.trend.sma_indicator(df['Close'], window=5)
        df['MA_10'] = ta.trend.sma_indicator(df['Close'], window=10)
        df['MA_20'] = ta.trend.sma_indicator(df['Close'], window=20)
        
        # RSI
        df['RSI'] = ta.momentum.rsi(df['Close'], window=14)
        
        # MACD
        macd = ta.trend.MACD(df['Close'])
        df['MACD'] = macd.macd()
        df['MACD_signal'] = macd.macd_signal()
        df['MACD_histogram'] = macd.macd_diff()
        
        # Bollinger Bands
        bollinger = ta.volatility.BollingerBands(df['Close'])
        df['BB_upper'] = bollinger.bollinger_hband()
        df['BB_lower'] = bollinger.bollinger_lband()
        df['BB_middle'] = bollinger.bollinger_mavg()
        
        # Volume indicators
        df['Volume_MA'] = ta.trend.sma_indicator(df['Volume'], window=10)
        
        # Price change
        df['Price_Change'] = df['Close'].pct_change()
        df['Price_Change_MA'] = ta.trend.sma_indicator(df['Price_Change'], window=5)
        
        return df
    
    def prepare_data_for_ml(self, data, target_column='Close', sequence_length=60):
        """Prepare data for machine learning"""
        # Add technical indicators
        df = self.add_technical_indicators(data)
        
        # Select features
        feature_columns = [
            'Open', 'High', 'Low', 'Close', 'Volume',
            'MA_5', 'MA_10', 'MA_20', 'RSI', 'MACD', 
            'MACD_signal', 'BB_upper', 'BB_lower', 'BB_middle',
            'Volume_MA', 'Price_Change', 'Price_Change_MA'
        ]
        
        # Remove rows with NaN values
        df = df.dropna()
        
        if len(df) < sequence_length:
            raise ValueError(f"Not enough data. Need at least {sequence_length} rows, got {len(df)}")
        
        # Prepare features and target
        features = df[feature_columns].values
        target = df[target_column].values
        
        # Create sequences
        X, y = [], []
        for i in range(sequence_length, len(features)):
            X.append(features[i-sequence_length:i])
            y.append(target[i])
        
        return np.array(X), np.array(y), df[feature_columns].columns.tolist()
    
    def get_latest_data_for_prediction(self, data, sequence_length=60):
        """Get the latest data sequence for prediction"""
        df = self.add_technical_indicators(data)
        
        feature_columns = [
            'Open', 'High', 'Low', 'Close', 'Volume',
            'MA_5', 'MA_10', 'MA_20', 'RSI', 'MACD', 
            'MACD_signal', 'BB_upper', 'BB_lower', 'BB_middle',
            'Volume_MA', 'Price_Change', 'Price_Change_MA'
        ]
        
        df = df.dropna()
        features = df[feature_columns].values
        
        if len(features) < sequence_length:
            raise ValueError(f"Not enough data for prediction. Need at least {sequence_length} rows")
        
        return features[-sequence_length:].reshape(1, sequence_length, -1)
